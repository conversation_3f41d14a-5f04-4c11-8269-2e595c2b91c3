<?php

namespace Features\Setup\Contexts;

use AwardForce\Library\Enums\ScopeOption;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use Carbon\Carbon;
use FormSelector;

trait FormsFeatures
{
    protected $grantForm;
    protected $forms;
    protected $draftSeasonForm;
    protected $selectedForm;

    /**
     * @Given Max forms is set to :size
     */
    public function maxFormsIsSetTo(int $size): void
    {
        current_account()->formQuantityLimit = $size;
        current_account()->save();
    }

    /**
     * @Given form limit is reached
     */
    public function formLimitReached(): void
    {
        // One default form is created by the account factory
        $amount = current_account()->formQuantityLimit - 1;
        $this->forms = $this->muffins($amount, Form::class);
    }

    /**
     * @Then I should be able to access new form page for :type form
     */
    public function iShouldBeAbleToAccessNewFormPage(?string $type = null): void
    {
        $this->route('GET', 'forms.new', ['type' => $type]);
        $this->assertResponseOk();
    }

    /**
     * @Then I select :type form
     */
    public function iSelectForm(?string $type): void
    {
        $this->selectedForm = Form::where('type', $type)->first();
    }

    /**
     * @Then I try to copy the form
     */
    public function iTryToCopyTheForm(): void
    {
        $this->response = $this->route('POST', 'forms.copy', ['form' => $this->selectedForm->slug]);
    }

    /**
     * @Then I should not be able to access new form page for entry form
     */
    public function iShouldNotBeAbleToAccessNewFormPageForEntryForm(): void
    {
        $this->route('GET', 'forms.new', ['type' => 'entry']);
        $this->assertResponseStatus(302);
    }

    /**
     * @Then I should not be able to access new form page for report form
     */
    public function iShouldNotBeAbleToAccessNewFormPageForReportForm(): void
    {
        $this->route('GET', 'forms.new', ['type' => 'report']);
        $this->assertResponseStatus(404);
    }

    /**
     * @Then I should be able to access to edit form page
     */
    public function iShouldBeAbleToAccessEditFormPage()
    {
        $this->route('GET', 'forms.settings', ['form' => FormSelector::get()->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Then I Should see an alert text about multiform in trial account
     */
    public function iShouldSeeAnAlertTextAboutMultiformInTrialAccount()
    {
        $this->assertResponseContains(trans('form.messages.multiform_trial_alert'));
    }

    /**
     * @Then I should be able to edit form settings
     */
    public function iShouldBeAbleToEditFormSettings()
    {
        $data = [
            'translated' => ['name' => ['en_GB' => 'Entry form name']],
            'type' => 'entry',
            'chapterOption' => 'all',
        ];

        $this->route('POST', 'forms.settings', ['form' => FormSelector::get()->slug], $data);
    }

    /**
     * @Given A number of forms exist
     */
    public function aNumberofFormsExist()
    {
        $this->forms = $this->muffins(4, Form::class);
    }

    /**
     * @Given A grant report form exists
     */
    public function aGrantReportFormExists(): void
    {
        $this->grantForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
    }

    /**
     * @Given a form exists for the draft season
     */
    public function aFormExistsForTheDraftSeason(): void
    {
        $this->draftSeasonForm = $this->muffin(Form::class, ['season_id' => $this->draftSeason->id]);
    }

    /**
     * @Then I Should be able to delete that grant report form
     */
    public function IShouldBeAbleToDeleteThatGrantReportForm(): void
    {
        $this->route('DELETE', 'forms.delete', [], ['selected' => [$this->grantForm->id]]);

        $this->assertNoErrors();
        $this->assertRedirectedToRoute('forms.index');

        $this->assertTrue($this->grantForm->fresh()->trashed());
    }

    /**
     * @Then I Should not be able to delete an entry form if only one entry form exists
     */
    public function IShouldNotBeAbleToDeleteAnEntryFormIfOnlyOneEntryFormExists(): void
    {
        $form = app(FormRepository::class)->getOneBy('account_id', current_account_id());

        $this->route('DELETE', 'forms.delete', [], ['selected' => [$form->id]]);

        $this->assertNoErrors();
        $this->assertRedirectedToRoute('forms.index');

        $this->assertFalse($form->fresh()->trashed());
    }

    /**
     * @Then I Should be able to delete an entry form
     */
    public function IShouldBeAbleToDeleteAnEntryForm(): void
    {
        $this->route('DELETE', 'forms.delete', [], ['selected' => [$this->forms[0]->id]]);

        $this->assertNoErrors();
        $this->assertRedirectedToRoute('forms.index');

        $this->assertTrue($this->forms[0]->fresh()->trashed());
    }

    /**
     * @Then I set the first form
     */
    public function iSetTheFirstForm()
    {
        FormSelector::set($this->forms[0]);
    }

    /**
     * @Then I should be able to edit form role settings
     */
    public function iShouldBeAbleToEditFormRoleSettings()
    {
        $form = FormSelector::get();

        $payload = [
            'translated' => ['name' => ['en_GB' => 'Entry form name']],
            'type' => 'entry',
            'settings' => [
                'roleVisibility' => 'select',
            ],
            'roles' => $this->user->roles->pluck('slug')->map(fn($slug) => (string) $slug)->toArray(),
            'rounds' => [
                [
                    'selected' => true,
                    'startsAt' => Carbon::now()->format('Y-m-d H:i'),
                    'startsTz' => 'UTC',
                    'endsAt' => Carbon::now()->addMonths(3)->format('Y-m-d H:i'),
                    'endsTz' => 'UTC',
                    'translated' => [],
                ],
            ],

        ];

        //Default value is 'all' and roles should be empty
        $this->assertTrue($form->settings->roleVisibility === ScopeOption::All);
        $this->assertTrue($form->roles->count() === 0);
        $this->route('POST', 'forms.update', ['form' => FormSelector::get()->slug], $payload);
        $this->assertRedirectedTo('/');
    }

    /**
     * @Then form should have been updated
     */
    public function formShouldHaveBeenUpdated()
    {
        $form = FormSelector::get();
        $form->refresh();

        $this->assertTrue($form->settings->roleVisibility === ScopeOption::Select);
        $this->assertTrue($form->roles->count() === $this->user->roles->count());
    }
}
