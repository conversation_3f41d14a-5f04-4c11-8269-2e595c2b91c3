<?php

namespace Tests\Stubs;

use AwardForce\Library\Composers\CacheableComposer;
use AwardForce\Library\Composers\ComposeCache;
use Illuminate\Contracts\View\View;

class FakeSpecificCacheableComposer implements CacheableComposer
{
    use ComposeCache;

    public static $composeCounter = 0;

    public function compose(View $view): void
    {
        self::$composeCounter++;

        $view->with(['foo' => 'bar']);
    }

    protected function cacheableKeys(): array
    {
        return ['foo'];
    }
}
