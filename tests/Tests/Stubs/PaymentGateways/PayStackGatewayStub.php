<?php

namespace Tests\Stubs\PaymentGateways;

use AwardForce\Modules\Payments\Gateways\PayStack;

class PayStackGatewayStub extends PayStack
{
    public function __construct(
        string $publicKey,
        string $secretKey,
        string $merchantEmail,
        bool $testMode
    ) {
        parent::__construct($publicKey, $secretKey, $merchantEmail, true);
    }

    public function setGateway($gateway)
    {
        $this->gateway = $gateway;
    }
}
