<?php

namespace Tests\Modules\Api\V2\Services;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Api\V2\Services\ApiRequestUtils;
use Dingo\Api\Routing\Helpers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\Group;
use Platform\Language\Language;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\IntegratedTestCase;

#[Group('apiV2')]
final class ApiRequestUtilsTest extends IntegratedTestCase
{
    use ApiRequestUtils;
    use Helpers;

    /** @var Request */
    private $request;

    public function init()
    {
        Language::setLanguages(['en_GB' => 'English', 'it_IT' => 'Italia']);

        $account = new Account();
        $account->save();
        $account->addLanguage(new Language('en_GB'), true);
        $account->addLanguage(new Language('it_IT'));

        CurrentAccount::set($account);

        $this->request = new Request();
    }

    public function testNoLanguagesRequested(): void
    {
        $languages = $this->apiLanguagesFromHeader($this->request);

        $this->assertEquals([current_account()->defaultLanguage()->code()], $languages);
    }

    public function testAllLanguagesRequested(): void
    {
        $this->request->headers->set('x-api-language', 'all');
        $languages = $this->apiLanguagesFromHeader($this->request);

        $this->assertEquals(['all'], $languages);
    }

    public function testSingleLanguagerequested(): void
    {
        $this->request->headers->set('x-api-language', 'en_GB');
        $languages = $this->apiLanguagesFromHeader($this->request);

        $this->assertEquals(['en_GB'], $languages);
    }

    public function testMultipleLanguagesRequested(): void
    {
        $this->request->headers->set('x-api-language', 'en_GB,it_IT');
        $languages = $this->apiLanguagesFromHeader($this->request);

        $this->assertEquals(['en_GB', 'it_IT'], $languages);
    }

    public function testUnsupportedLanguage(): void
    {
        $this->expectException(HttpException::class);

        $this->request->headers->set('x-api-language', 'zz_ZZ');
        $this->apiLanguagesFromHeader($this->request);
    }

    public function testValidTranslationLanguage(): void
    {
        $translations = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $this->assertTrue($this->getClosureValidator($translations)->passes());
    }

    public function testInvalidTranslationLanguage(): void
    {
        $translations = [
            'name' => [
                'en_GZ' => 'Test Name',
            ],
        ];
        $this->assertFalse($this->getClosureValidator($translations)->passes());
    }

    private function getClosureValidator(array $data)
    {
        return Validator::make($data, [
            'name' => function ($attribute, $value, $fail) use ($data) {
                return $this->validateRequestBodyLanguageCodes($data, $fail);
            },
        ]);
    }
}
