<?php

namespace Tests\Modules\Integrations\Plagiarism\Commands;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Integrations\Plagiarism\Commands\StartEntryPlagiarismScan;
use AwardForce\Modules\Integrations\Plagiarism\Commands\StartEntryPlagiarismScanHandler;
use AwardForce\Modules\Integrations\Plagiarism\Copyleaks;
use Illuminate\Support\Facades\Event;
use Platform\Events\EventDispatcher;
use Platform\Test\EventAssertions;
use Tests\IntegratedTestCase;

final class StartEntryPlagiarismScanTest extends IntegratedTestCase
{
    use EventAssertions, EventDispatcher;

    /** @var PlagiarismDetection */
    protected $integration;

    /** @var Entry */
    private $entry;

    public function init()
    {
        $this->entry = $this->muffin(Entry::class, ['submitted_at' => now()]);

        $this->integration = new Copyleaks;
        $this->integration->accountId = $this->account->id;
        $this->integration->seasonId = $this->entry->seasonId;
        $this->integration->driver = 'copyleaks';
        $this->integration->save();
    }

    public function testCommand(): void
    {
        $command = new StartEntryPlagiarismScan($this->integration, $this->entry);

        $handler = new StartEntryPlagiarismScanHandler(
            $this->app->make(ValuesService::class)
        );

        $handler->handle($command);

        Event::shouldReceive('dispatch');
    }
}
