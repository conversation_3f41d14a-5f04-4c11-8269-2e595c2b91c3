<?php

namespace Tests\Modules\Ecommerce\Cart;

use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\EntrantItemiser;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderItem;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Models\Tax;
use AwardForce\Modules\Seasons\Models\Season;
use Consumer;
use Tests\IntegratedTestCase;

final class EntrantItemiserTest extends IntegratedTestCase
{
    public function init()
    {
        $this->muffin(Tax::class);

        $this->user = $this->muffin(User::class);
    }

    private function openCart(string $currency)
    {
        $cart = Cart::open(Consumer::user());
        $cart->setCurrency(new Currency($currency));

        return $cart;
    }

    public function testDetectsEntrantFeeWasPaidInTheCurrentSeason(): void
    {
        $this->openCart('AUD');
        $itemiser = app(EntrantItemiser::class);

        $this->assertFalse($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));

        $order = $this->muffin(Order::class, ['user_id' => $this->user->id]);
        $this->muffin(OrderItem::class, ['order_id' => $order->id, 'item_type' => OrderItem::TYPE_ENTRANT]);

        $this->assertTrue($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));
    }

    public function testIgnoresEntrantFeeFromPastSeasons(): void
    {
        $oldSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $newSeason = $this->season;

        $order = $this->muffin(Order::class, ['user_id' => $this->user->id, 'season_id' => $oldSeason->id]);
        $this->muffin(OrderItem::class, ['order_id' => $order->id, 'item_type' => OrderItem::TYPE_ENTRANT]);

        $this->openCart('AUD');
        $itemiser = app(EntrantItemiser::class);

        $this->assertTrue($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $oldSeason->id));
        $this->assertFalse($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $newSeason->id));
    }

    public function testReturnsEntrantPricesFromActiveSeason(): void
    {
        $oldSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);

        $price = $this->muffin(Price::class, ['type' => Price::TYPE_ENTRANT, 'default' => true]);

        $this->openCart('AUD');
        $itemiser = app(EntrantItemiser::class);

        $this->assertEquals($price->id, $itemiser->getPrice($this->season->id)->id);
        $this->assertNull($itemiser->getPrice($oldSeason->id));
    }

    public function testReturnsFirstPriceIfNoDefaultAvailable(): void
    {
        $priceA = $this->muffin(Price::class, ['type' => Price::TYPE_ENTRANT, 'default' => false]);
        $this->muffin(Price::class, ['type' => Price::TYPE_ENTRANT, 'default' => false]);

        $this->openCart('AUD');
        $itemiser = app(EntrantItemiser::class);

        $this->assertEquals($priceA->id, $itemiser->getPrice($this->season->id)->id);
    }

    public function testDetectOrderWasPaidInTheCurrentSeasonButTheOrderWasDeleted()
    {
        $this->openCart('AUD');
        $itemiser = app(EntrantItemiser::class);

        $this->assertFalse($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));

        $order = $this->muffin(Order::class, ['user_id' => $this->user->id]);
        $this->muffin(OrderItem::class, ['order_id' => $order->id, 'item_type' => OrderItem::TYPE_ENTRANT]);

        $this->assertTrue($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));

        $order->delete();

        $this->assertFalse($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));
    }

    public function testDetectOrderWasCreatedInTheCurrentSeasonButItWasNotPaid()
    {
        $this->openCart('AUD');
        $itemiser = app(EntrantItemiser::class);

        $this->assertFalse($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));

        $order = $this->muffin(Order::class, [
            'user_id' => $this->user->id,
            'payment_status' => Order::PAYMENT_STATUS_AWAITING_PAYMENT,
        ]);
        $this->muffin(OrderItem::class, ['order_id' => $order->id, 'item_type' => OrderItem::TYPE_ENTRANT]);

        $this->assertTrue($itemiser->userHasPaidOrWillPayEntrantFee($this->user->id, $this->season->id));
    }
}
