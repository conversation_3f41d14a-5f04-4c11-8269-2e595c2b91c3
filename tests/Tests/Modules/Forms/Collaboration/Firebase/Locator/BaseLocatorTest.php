<?php

namespace Tests\Modules\Forms\Collaboration\Firebase\Locator;

use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\BaseLocator;
use Tests\LightUnitTestCase;

class BaseLocatorTest extends LightUnitTestCase
{
    private BaseLocator $locator;

    public function init(): void
    {
        $this->locator = new FakeLocator;
    }

    public function testItGeneratesCorrectPath(): void
    {
        $path = $this->locator->path('testFieldPath');

        $this->assertSame('lockables', $path->collection);
        $this->assertSame('part1-part2', $path->documentId);
        $this->assertSame('testFieldPath', $path->fieldPath);
    }
}

readonly class FakeLocator extends BaseLocator
{
    public function documentParts(): array
    {
        return ['part1', 'part2'];
    }
}
