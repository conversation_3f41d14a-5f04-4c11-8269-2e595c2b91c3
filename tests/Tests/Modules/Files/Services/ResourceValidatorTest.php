<?php

namespace Tests\Modules\Files\Services;

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\ResourceValidator;
use AwardForce\Modules\Files\Services\Uploader;
use Mockery as m;
use Tests\UnitTestCase;

final class ResourceValidatorTest extends UnitTestCase
{
    public function testSetupUploader(): void
    {
        $uploader = m::mock(Uploader::class);
        $uploader->shouldReceive('setResource')->with('test-resource', 123)->once()->andReturnSelf();
        $uploader->shouldReceive('setFileTypes')->with([])->once()->andReturnSelf();
        $uploader->shouldReceive('setMaxFileSize')->once();

        (new FakeValidator)->setupUploader($uploader, 123);
    }

    public function testValidatesMatchingResource(): void
    {
        $validator = new FakeValidator;

        $this->assertTrue($validator->validates(new File(['resource' => $validator->resource()])));
        $this->assertFalse($validator->validates(new File(['resource' => 'invalid-resource'])));
    }

    public function testValidateExtensionAllowed(): void
    {
        $validator = (new FakeValidator)->setAllowedTypes(['images' => ['jpg', 'jpeg', 'gif'], 'mp3', 'avi', 'aac']);

        $this->assertTrue($validator->check(new File(['original' => 'filename.mp3'])));     // directly in rules
        $this->assertTrue($validator->check(new File(['original' => 'filename.jpeg'])));    // nested in rules
        $this->assertTrue($validator->check(new File(['original' => 'filename.JPG'])));    // case ignored
        $this->assertFalse($validator->check(new File(['original' => 'filename.txt'])));    // not included
    }

    public function testValidateExtensionWithEmptyAllowedArray(): void
    {
        $this->assertTrue((new FakeValidator)->check(new File(['original' => 'filename.txt'])));
    }

    public function testValidateFileSizeWithinLimits(): void
    {
        $validator = (new FakeValidator)->setSizes(1, 3);   // this is in megabytes btw

        $this->assertFalse($validator->check(new File(['size' => 10])));    // too small (this is bytes)
        $this->assertTrue($validator->check(new File(['size' => 2 * ResourceValidator::MEGABYTE]))); // 2MB is OK
        $this->assertFalse($validator->check(new File(['size' => 4 * ResourceValidator::MEGABYTE]))); // 4MB is too big
    }
}

class FakeValidator extends ResourceValidator
{
    private $allowedTypes = [];
    private $minSize = 0;
    private $maxSize = 0;

    public function resource(): string
    {
        return 'test-resource';
    }

    public function setAllowedTypes(array $allowedTypes)
    {
        $this->allowedTypes = $allowedTypes;

        return $this;
    }

    public function allowedTypes(): array
    {
        return $this->allowedTypes;
    }

    public function setSizes(int $minSize, int $maxSize)
    {
        $this->minSize = $minSize;
        $this->maxSize = $maxSize;

        return $this;
    }

    public function minSize(): int
    {
        return $this->minSize;
    }

    public function maxSize(): int
    {
        return $this->maxSize;
    }
}
