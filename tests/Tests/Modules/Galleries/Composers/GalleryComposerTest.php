<?php

namespace Tests\Modules\Galleries\Composers;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Galleries\Composers\GalleryComposer;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Illuminate\Contracts\View\View;
use Illuminate\Pagination\LengthAwarePaginator;
use Mockery as m;
use Tests\IntegratedTestCase;

final class GalleryComposerTest extends IntegratedTestCase
{
    private $view;

    /** @var ScoreSet */
    private $scoreSet;

    public function init()
    {
        $this->scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_GALLERY]);
        $this->setupUserWithRole('Judge');
        $this->view = m::spy(View::class);
        $this->view->scoreSet = $this->scoreSet;
    }

    public function testBulkDownloadEnabled(): void
    {
        $this->scoreSet->bulkDownload = true;
        $this->scoreSet->contentBlockId = ($contentBlock = $this->muffin(ContentBlock::class))->id;
        $this->scoreSet->save();
        $this->view->assignments = new LengthAwarePaginator([], 4, 100);

        app(GalleryComposer::class)->compose($this->view);

        $this->view->shouldHaveReceived('with')->withArgs(function (array $options) use ($contentBlock) {
            $this->assertSame($options['bulkDownloadEnabled'], true);
            $this->assertSame($options['contentBlock']->id, $contentBlock->id);

            return true;
        });
    }

    public function testAssignments(): void
    {
        /** @var Assignment $assignment */
        $assignment = $this->muffin(Assignment::class, [
            'status' => Assignment::STATUS_COMPLETE,
            'score_set_id' => $this->scoreSet->id,
            'judge_id' => $this->user->id,
        ]);
        $this->muffins(3, Assignment::class, [
            'status' => Assignment::STATUS_COMPLETE,
            'score_set_id' => $this->scoreSet->id,
            'judge_id' => $this->user->id + 2,
        ]);

        $this->view->assignments = new LengthAwarePaginator([$assignment], 1, 100);
        \Consumer::set(new UserConsumer($this->user));

        app(GalleryComposer::class)->compose($this->view);

        $this->view->shouldHaveReceived('with')->withArgs(function (array $options) use ($assignment) {
            $chapters = $options['chapters'];
            $categories = $options['categories'];
            $selectorCategories = $options['selectorCategories'];

            $this->assertSame($assignment->entry->chapterId, $chapters->first()->id);
            $this->assertSame(
                (string) ($category = $assignment->entry->category)->slug,
                (string) $categories->first()->slug
            );
            $this->assertSame([(string) $category->slug => translate($category)->name], $selectorCategories->all());

            return true;
        });
    }
}
