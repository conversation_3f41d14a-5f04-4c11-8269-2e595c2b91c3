declare type UnionToIntersection<U> = (U extends unknown ? (k: U) => void : never) extends (k: infer I) => void
	? I
	: never;

declare type EventDef = Record<string, ((...args: unknown[]) => void) | null>;

/*
 * This type is used to define the type of the event emitter.
 *
 * It takes events type i.e.:
 * type SomeEvents = {
	'eventA': (x: string) => void;
	'eventB': (y: number) => void; };
 *
 * and returns intersection of emitter function signatures i.e.:
 * type SomeEmitters = ((event: 'eventA', x: string) => void) & ((event: 'eventB', y: number) => void)
 *
 * with this we can use it in component like:
 * setup(props: Props, context: SetupContext<SomeEvents>): View {
		return controller(props, context.emit);
	},
 *
 * and in controller we can use it like:
 * const controller = (props: Props, emit: EventEmitters<SomeEvents>): View => { ... }
 *
 * so that inside controller we can emit events like:
 * emit('eventA', 'some string');
 * emit('eventB', 123);
 */
declare type EventEmitters<
	Options = EventDef,
	Event extends keyof Options = keyof Options
> = Options extends (infer V)[]
	? (event: V, ...args: unknown[]) => void
	: unknown extends Options
	? (event: string, ...args: unknown[]) => void
	: UnionToIntersection<
			{
				[key in Event]: Options[key] extends (...args: infer Args) => void ? (event: key, ...args: Args) => void : never;
			}[Event]
	  >;

export { EventEmitters, EventDef };
