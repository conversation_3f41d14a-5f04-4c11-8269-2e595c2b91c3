<template>
	<modal
		ref="modal"
		v-model="isModalOpen"
		style="position: relative"
		:header="false"
		:footer="true"
		:close-on-backdrop-clicks="false"
		:confirm-button="false"
		:close-button="true"
		:close-button-label="lang.get('buttons.close')"
		@backdrop-clicked="onClose"
		@closed="onClose"
	>
		<close-icon slot="before-content" @click.prevent.stop="onClose" />

		<h4 class="modal-title">{{ title }}</h4>
		<loader v-if="showLoader" id="list-loader" type="basic" :top="40"></loader>
		<div v-else>
			<div class="row">
				<div v-for="parameter in cart.parameters" :key="parameter.label">
					<description-list-item :label="parameter.label">
						<template slot="value">
							<div v-output="parameter.value"></div>
						</template>
					</description-list-item>
				</div>
			</div>
			<br />
			<div class="row">
				<div class="col-xs-12">
					<a class="action-button" @click.prevent="convert">{{ lang.get('ecommerce.carts.actions.convert') }}</a>
					<a class="action-button" @click.prevent="empty">{{ lang.get('ecommerce.carts.actions.empty_reset') }}</a>
				</div>
			</div>
			<div v-output="cart.details"></div>
		</div>

		<confirmation-simple-modal
			v-if="confirmation.isOpen"
			:confirmation="confirmation.prompt"
			@confirmed="confirmation.action"
			@closed="confirmation.cancel"
		/>
	</modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useController } from '@/domain/services/Composer';
import { cartModalController, View } from './CartModal.controller';
import { Modal } from 'vue-bootstrap';
import CloseIcon from '@/lib/components/ListActions/Partials/CloseIcon.vue';
import ConfirmationSimpleModal from '@/lib/components/Shared/ConfirmationSimpleModal.vue';
import Loader from '@/lib/components/Loader';
import DescriptionListItem from '@/lib/components/Shared/DescriptionListItem.vue';

export default defineComponent<View>({
	components: {
		CloseIcon,
		Modal,
		ConfirmationSimpleModal,
		Loader,
		DescriptionListItem,
	},

	props: {},

	setup: useController(cartModalController, 'cartModalController'),
});
</script>

<!-- eslint-disable max-len -->
<style lang="scss" scoped>
#list-loader {
	padding: 80px;
}
</style>
