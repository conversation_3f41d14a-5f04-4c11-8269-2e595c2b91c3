<template>
	<div :class="`alert-${alertLevel} sticky`" role="alert">
		<div class="icon">
			<div :class="`af-icons-md af-icons-md-alert-${alertLevel}`"></div>
		</div>
		<div class="message">
			<p>
				{{ lang.get('tabs.configuration.feature-disabled') }}
				<a :href="`/feature-disabled/${actualFeature}`" target="_blank" rel="noreferrer noopener">{{
					lang.get('shared.learn_more')
				}}</a>
			</p>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { useController } from '@/domain/services/Composer';
import {
	tabTypeFeatureDisabledAlertController,
	Props,
	View,
} from '@/modules/entry-form/Configuration/TabConfigurator/TabTypeFeatureDisabledAlert.controller';
import { AlertLevel } from '@/lib/types/AlertLevel';

export default defineComponent<Props, View>({
	inject: ['lang'],
	props: {
		feature: {
			type: String,
			required: true,
		},
		alertLevel: {
			type: String as PropType<AlertLevel>,
			default: 'info',
		},
		displayFeature: {
			type: String,
			default: '',
		},
	},
	setup: useController(tabTypeFeatureDisabledAlertController, 'TabTypeFeatureDisabledAlertController') as () => View,
});
</script>
