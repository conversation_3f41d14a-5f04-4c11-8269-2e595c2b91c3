<?php
$name = (isset($name) ? $name : 'values').'['.$field->slug.']';
$for = str_slug($name);
?>

<div class="form-group {{ form_field_id($field->slug).' '.FormError::classIfError($name) }}">
    {!! html()->select($name, $options, $field->value)->attributes(['id' => $for, 'class' => $field->autocomplete ? 'form-control autocomplete' : 'form-control']) !!}
    {!! FormError::message($name) !!}
</div>
