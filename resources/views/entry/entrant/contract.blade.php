@section('title')
    {!! HTML::pageTitle($contentBlock->title($contentBlockData)) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('entries.titles.entrant'), route('entry.entrant.index')],
                    [$contentBlock->title($contentBlockData)],
                ]])
            </div>
        </div>
    </div>

    @include('users.confirm', ['redirect' => Request::url()])
    @include('partials.errors.display')
    @include('partials.errors.message')

    <div class="row island">
        <div class="col-xs-12 col-md-6">
            <div class="text-content markdown-render">
                {!! $contentBlock->content($contentBlockData) !!}
            </div>
        </div>

        <div class="col-xs-12 col-md-6">
            {!! html()->form(action:route('entry.entrant.sign-contract', ['entry' => $entry->slug, 'contract' => $contract->slug]))->attribute('files', true)->open() !!}
                <signature
                    id="signature"
                    :can-sign="{{ boolean_string_value($canSign) }}"
                    :translations="@js($translations)"
                    language="{{ \Consumer::languageCode() }}"
                    default-language="{{ \CurrentAccount::defaultLanguage()->code }}"
                    :links="@js(config('links.'.current_account_brand()))"
                    :features=" {{ account_features_for_vue() }}"
                    uploader-options="{{ $uploaderOptions }}"
                    :contract="@js($contract)"
                    :routes="@jsObject($signatureRoutes)"
                >
                </signature>
            {!! html()->form()->close() !!}
        </div>
    </div>
@stop
