<?php $searching = query_parameters([]); ?>

@section('title')
    {!! HTML::pageTitle(trans('documents.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <documents-list id="documents-list" :ids="@js($documentIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <h1>{{ trans('documents.titles.main') }}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => $searching, 'disableAdvanced' => true])
                @endcomponent
            </div>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $documents])
                    </div>
                </div>
            </div>

            <div>
                @if ($documents->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $documents->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $documents])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('documents.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </documents-list>
@stop
