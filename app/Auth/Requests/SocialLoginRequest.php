<?php

namespace AwardForce\Auth\Requests;

use AwardForce\Auth\Models\SettingRepository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class SocialLoginRequest extends FormRequest
{
    protected $settings;

    public function __construct(SettingRepository $settings)
    {
        $this->settings = $settings;
    }

    public function rules()
    {
        return [];
    }

    public function authorize()
    {
        $providers = $this->settings->getValueByKey($this->route('accountSlug'), 'social-authentication');

        return Str::contains($providers, $this->route('provider'));
    }
}
