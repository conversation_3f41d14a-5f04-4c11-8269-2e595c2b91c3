<?php

namespace AwardForce\Modules\Audit\Search\Filters;

use Illuminate\Contracts\Support\Htmlable;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class ResourceFilter implements ColumnatorFilter, Htmlable, SearchFilter
{
    /** @var array */
    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    public function applyToEloquent($query)
    {
        if ($resource = ($this->input['resource'] ?? null)) {
            $query->where('resource', $resource);
        }

        return $query;
    }

    public function applies(): bool
    {
        return (bool) ($this->input['resource'] ?? false);
    }

    public function toHtml()
    {
        $resources = ['' => ''] + sort_config_list('audit.resources', 'audit.resources');

        return view('audit.filters.resource', ['resources' => $resources])->render();
    }
}
