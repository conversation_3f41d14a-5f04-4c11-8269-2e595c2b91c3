<?php

namespace AwardForce\Modules\AllocationPayments\Events;

use AwardForce\Library\Exceptions\UnsupportedCurrencyException;
use AwardForce\Modules\AllocationPayments\Commands\UpdateAllocationPaymentCommand;
use AwardForce\Modules\AllocationPayments\Commands\UpdateAllocationPaymentCommandHandler;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class AllocationPaymantWasUpdatedTest extends BaseTestCase
{
    use Database;
    use Laravel;

    /**
     * @throws UnsupportedCurrencyException
     */
    public function testAllocationPaymentWasUpdatedEventWasDispatched(): void
    {
        Event::fake();
        $allocationPayment = $this->muffin(AllocationPayment::class);

        $handler = app(UpdateAllocationPaymentCommandHandler::class);
        $handler->handle(new UpdateAllocationPaymentCommand(
            $allocationPayment,
            (string) $allocationPayment->paymentMethod->slug,
            $allocationPayment->status,
            $allocationPayment->reference.'22348234hwedfihudsf',
            $allocationPayment->amount->value(),
            $allocationPayment->allocationId,
            $allocationPayment->getFormattedDateDue(),
            $allocationPayment->getFormattedDatePaid(),
        ));

        Event::assertDispatched(AllocationPaymentWasUpdated::class);
    }
}
