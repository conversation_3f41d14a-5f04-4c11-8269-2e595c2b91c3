<?php

namespace AwardForce\Modules\AllocationPayments\Traits;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\AllocationPayments\Repositories\AllocationPaymentsRepository;
use AwardForce\Modules\Comments\Services\AddComments;
use AwardForce\Modules\Comments\Tags\AllocationPaymentTag;
use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use AwardForce\Modules\PaymentMethods\Repositories\PaymentMethodsRepository;
use AwardForce\Modules\PaymentScheduleTemplates\Models\PaymentScheduleTemplate;
use AwardForce\Modules\PaymentScheduleTemplates\Repositories\PaymentScheduleTemplateRepository;
use CurrentAccount;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Config;
use Tectonic\LaravelLocalisation\Translator\Engine;

trait HasAllocationPayments
{
    public function allocationPaymentsTranslations(): array
    {
        VueData::registerTranslations([
            'audit.api',
        ]);

        return translations_for_vue(Consumer::languageCode(), [
            'allocation-payments.form',
            'allocation-payments.modal',
            'allocation-payments.buttons.comment',
            'allocation-payments.table',
            'buttons.edit',
            'buttons.save',
            'buttons.cancel',
            'buttons.delete',
            'buttons.comment',
            'buttons.continue',
            'miscellaneous.alerts.generic',
            'miscellaneous.alerts.delete.comment',
            'miscellaneous.alerts.validator.message',
        ]);
    }

    public function allocationPaymentsRoutes(): array
    {
        return routes_for_vue([
            'allocation-payment.allocation.index',
            'allocation-payment.create',
            'allocation-payment.update',
            'allocation-payment.delete',
            'allocation-payment.comments.index',
        ]);
    }

    public function allocationPaymentsScheduleTemplates(): array
    {
        return $this->getTranslator()
            ->translate((property_exists($this, 'paymentScheduleTemplates') ? $this->paymentScheduleTemplates : app(PaymentScheduleTemplateRepository::class))->getAll())
            ->map(function (PaymentScheduleTemplate $paymentScheduleTemplate) {
                $paymentScheduleTemplateArray = Arr::only($paymentScheduleTemplate->toArray(), [
                    'payments', 'slug',
                ]);
                $paymentScheduleTemplateArray['name'] = lang($paymentScheduleTemplate, 'name');
                $paymentScheduleTemplateArray['created_at'] = $paymentScheduleTemplate->created_at->format('Y-m-d');

                return $paymentScheduleTemplateArray;
            })
            ->toArray();
    }

    private function getTranslator(): Engine
    {
        return property_exists($this, 'translator') ? $this->translator : app(Engine::class);
    }

    public function allocationPayments(): array
    {
        $allocationPayments = $this->getTranslator()
            ->translate((property_exists($this, 'allocationPayments') ? $this->allocationPayments : app(AllocationPaymentsRepository::class))->getAll())
            ->values();

        return app(AddComments::class)
            ->toCollection($allocationPayments, AllocationPaymentTag::class)
            ->toArray();
    }

    public function statuses(): array
    {
        return collect(Config::get('allocation-payments.statuses'))
            ->map(function ($status) {
                return [
                    'key' => $status,
                    'name' => trans('allocation-payments.statuses.'.$status),
                ];
            })
            ->values()
            ->toArray();
    }

    public function paymentMethods(): array
    {
        return $this->getTranslator()
            ->translate(
                (property_exists($this, 'paymentMethods')
                    ? $this->paymentMethods
                    : app(PaymentMethodsRepository::class))->getWithTrashed()
            )
            ->sortBy(fn($e) => $e->order ?: PHP_INT_MAX)
            ->map(function (PaymentMethod $paymentMethod) {
                $paymentMethodArray = $paymentMethod->toArray();
                $paymentMethodArray['name'] = lang($paymentMethod, 'name');

                return $paymentMethodArray;
            })
            ->values()
            ->toArray();
    }

    public function permissions(): array
    {
        return [
            'hasPaymentPermissions' => Consumer::can('view', 'Payments'),
        ];
    }

    public function unitOptions(): array
    {
        return collect(Config::get('payment-schedule-templates.unit_options', []))
            ->map(function (array $data, string $unitOption) {
                return [
                    'key' => $unitOption,
                    'interval' => Arr::get($data, 'interval'),
                ];
            })
            ->values()
            ->toArray();
    }

    public function allocationPaymentsCommentsGlobalConfiguration(): array
    {
        return [
            'routes' => [
                'commentCreateUrl' => route('comment.create').'#comment-list',
                'commentUpdateUrl' => '/comment/', // Comment slug will be appended in js
                'commentDeleteUrl' => '/comment/', // Comment slug will be appended in js
            ],
            'labels' => [
                'placeholder' => trans('comments.cta.default'),
                'saveComment' => trans('buttons.comment'),
            ],
            'userId' => Consumer::id(),
            'defaultLanguage' => CurrentAccount::defaultLanguageCode(),
            'language' => Consumer::languageCode(),
        ];
    }
}
