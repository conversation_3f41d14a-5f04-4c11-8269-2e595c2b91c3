<?php

namespace AwardForce\Modules\Dashboard\View\Widgets;

use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Dashboard\View\DashboardCacheTimes;
use AwardForce\Modules\Dashboard\View\SeasonFormSelector;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class CategoryEntries extends View
{
    use DashboardCacheTimes;
    use SeasonFormSelector;

    public function __construct(
        private CategoryRepository $categories,
        private Engine $translator
    ) {
    }

    public function categories()
    {
        return $this->translator->shallow(
            $this->categories->flexibleCache($this->freshTtl(), $this->staleTtl())->getEntrySummariesByCategory($this->seasonId(), $this->getActiveFormId())
        );
    }
}
