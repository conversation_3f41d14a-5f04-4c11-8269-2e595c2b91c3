<?php

namespace AwardForce\Modules\Documents\Services;

use AwardForce\Modules\Documents\Contracts\DocumentGenerator as DocumentGeneratorContract;
use AwardForce\Modules\Documents\Models\Document;
use Exception;
use ZipArchive;

class LocalDocumentGenerator implements DocumentGeneratorContract
{
    use HandleTempFiles;
    use VariableSubstitutionsForWord;

    public function __construct(private WordToPDFConverter $wordToPDFConverter)
    {
    }

    /**
     * @throws Exception
     */
    public function handle(string $fileContent, string $fileType, array $mergeFields = []): string
    {
        $tempWordFile = $this->createTempWordFile($fileContent);
        $zip = $this->openZipArchive($tempWordFile);
        $this->processDocumentFiles($zip, $mergeFields);

        $zip->close();
        $content = file_get_contents($tempWordFile);
        unlink($tempWordFile);

        return $fileType === Document::FILE_TYPE_WORD ? base64_encode($content) : $this->wordToPDFConverter->execute($content);
    }

    private function processDocumentFiles(ZipArchive $zip, array $mergeFields): void
    {
        $this->getDocumentFiles($zip)
            ->each(function ($file) use ($zip, $mergeFields) {
                $content = $this->replaceVars($zip->getFromName($file), $mergeFields);
                $zip->deleteName($file);
                $zip->addFromString($file, $content);
            });

        $this->addHyperlinks($zip);
        $this->addMissingStyles($zip);
    }
}
