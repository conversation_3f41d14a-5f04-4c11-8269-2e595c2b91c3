<?php

namespace AwardForce\Modules\Identity\Users\Commands;

use AwardForce\Modules\Accounts\Services\MembershipService;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\UserFactory;
use AwardForce\Modules\Identity\Users\Services\GlobalCommunicationChannelService;
use Platform\Events\EventDispatcher;

class CreateUserFromApiCommandHandler
{
    use EventDispatcher;

    /**
     * @var MembershipService
     */
    private $memberships;

    /**
     * @var UserRepository
     */
    private $users;

    /**
     * @var UserFactory
     */
    private $userFactory;

    /**
     * @var GlobalCommunicationChannelService
     */
    private $communicationChannels;

    public function __construct(
        MembershipService $memberships,
        UserRepository $users,
        UserFactory $userFactory,
        GlobalCommunicationChannelService $communicationChannels
    ) {
        $this->users = $users;
        $this->userFactory = $userFactory;
        $this->communicationChannels = $communicationChannels;
        $this->memberships = $memberships;
    }

    public function handle($command)
    {
        $user = $this->users->getByEmail($command->email);

        if (! $user) {
            $user = $this->userFactory->addFromApi(
                $command->firstName,
                $command->lastName,
                $command->email,
                $command->mobile
            );

            $this->users->save($user);

            $this->communicationChannels->createChannelsAndConfirm($user);
        }

        if (! $user->currentMembership) {
            $this->memberships->registerMembership($user);
        }

        $this->dispatch($user->releaseEvents());

        return $user;
    }
}
