<?php

namespace AwardForce\Modules\Grants\Search;

use AwardForce\Library\Search\Columns\Form;
use AwardForce\Modules\Entries\Search\Columns\Category;
use AwardForce\Modules\Entries\Search\Columns\CategorySlug;
use AwardForce\Modules\Entries\Search\Columns\Chapter;
use AwardForce\Modules\Entries\Search\Columns\ChapterSlug;
use AwardForce\Modules\Entries\Search\Columns\Comments;
use AwardForce\Modules\Entries\Search\Columns\Contracts;
use AwardForce\Modules\Entries\Search\Columns\EligibilityStatus;
use AwardForce\Modules\Entries\Search\Columns\Entrant;
use AwardForce\Modules\Entries\Search\Columns\EntryFundAllocations;
use AwardForce\Modules\Entries\Search\Columns\LocalId;
use AwardForce\Modules\Entries\Search\Columns\ReviewStatus;
use AwardForce\Modules\Entries\Search\Columns\SubmissionStatus;
use AwardForce\Modules\Entries\Search\Columns\Title as EntryTitle;
use AwardForce\Modules\Forms\Collaboration\Search\Columns\CollaboratorsCount;
use AwardForce\Modules\Forms\Fields\Search\Columns\Search\TotalScoreColumn;
use AwardForce\Modules\Grants\Search\Columns\GrantEndDate;
use AwardForce\Modules\Grants\Search\Columns\GrantFundAllocations;
use AwardForce\Modules\Grants\Search\Columns\GrantParentCategory;
use AwardForce\Modules\Grants\Search\Columns\SimpleGrantStatus;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;

class UserGrantsColumnator extends ManageGrantsColumnator
{
    protected function baseColumns()
    {
        return new Columns([
            new LocalId,
            Slug::forResource('entries'),
            new EntryTitle,
            new Entrant,
            new SimpleGrantStatus,
            new EligibilityStatus,
            new GrantEndDate,
            new Category,
            new Chapter,
            app(Form::class),
            new EntryFundAllocations,
            new TotalScoreColumn,
            new SubmissionStatus,
            new ReviewStatus,
            new Contracts,
            new CategorySlug,
            new ChapterSlug,
            new GrantFundAllocations,
            new Comments,
            new GrantParentCategory,
            new CollaboratorsCount,
            Created::forResource('entries', consumer()->dateLocale()),
            Updated::forResource('entries', consumer()->dateLocale()),
        ]);
    }

    public static function key(): string
    {
        return 'grant.user.search';
    }

    public static function exportKey(): string
    {
        return '';
    }
}
