<?php

namespace AwardForce\Modules\Files\Commands;

use AwardForce\Library\Bus\QueuedJob;
use AwardForce\Modules\Identity\Users\Models\User;

class ReTranscodeFilesCommand extends QueuedJob
{
    /** @var User */
    public $user;

    /** @var array */
    public $fileIds;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string
     */
    public $queue = 'low';

    /**
     * @param  User  $user The user that the transcoding job is for. It is not always the owner of the files.
     */
    public function __construct(User $user, array $fileIds)
    {
        $this->user = $user;
        $this->fileIds = $fileIds;
    }
}
