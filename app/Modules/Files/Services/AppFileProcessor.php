<?php

namespace AwardForce\Modules\Files\Services;

use AwardForce\Modules\Files\Models\File;

class AppFileProcessor extends FileProcessor
{
    public static function getInstance(?int $tabId = null)
    {
        $fileProcessor = app(AppFileProcessor::class);
        $fileProcessor->tabId = $tabId;
        $fileProcessor->retrieveError = File::STATUS_REJECTED_NOT_FOUND;

        return $fileProcessor;
    }

    public function move(File $file, string $localFile)
    {
        $filename = self::filename(file_extension($file->original));

        if ($this->courier()->wasChunked($localFile)) {
            $this->courier()->copyToRemote($localFile, $filename);
        } else {
            $this->courier()->moveRemote($localFile, $filename);
        }

        $file->finalise($filename, $this->tabId);
    }
}
