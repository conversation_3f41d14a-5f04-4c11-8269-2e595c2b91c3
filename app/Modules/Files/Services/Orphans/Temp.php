<?php

namespace AwardForce\Modules\Files\Services\Orphans;

use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class Temp extends Remover
{
    public static string $DIR = 'temp';

    /**
     * Look for expired files in the `temp/` directory and remove them.
     */
    public function remove(): Collection
    {
        try {
            return collect($this->disk->allFiles(self::$DIR))
                ->filter(fn(string $file) => $this->expired($file))
                ->each(fn(string $file) => $this->delete($file));
        } catch (Exception $e) {
            Log::error("Temp file remover failed with: {$e->getMessage()}");

            return collect();
        }
    }
}
