<?php

namespace AwardForce\Modules\Judging;

use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Models\AssignmentCollection;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;

trait ConflictOfInterest
{
    public function canDeclareConflictOfInterest(ScoreSetCollection $scoreSets): bool
    {
        return $scoreSets->settingEnabled('conflictOfInterest');
    }

    protected function hasConflictOfInterest(AssignmentCollection $assignments)
    {
        return $assignments->reduce(function ($carry, Assignment $assignment) {
            return $carry || $assignment->conflictOfInterest;
        }, false);
    }
}
