<?php

namespace AwardForce\Modules\Entries\Events;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\ManageEntriesColumnator;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Grants\Search\ManageGrantsColumnator;
use AwardForce\Modules\Webhooks\Commands\DebounceableCallWebhookCommand;
use AwardForce\Modules\Webhooks\Listeners\EventListener;
use Illuminate\Support\Facades\Bus;
use Platform\Events\EventDispatcher;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Modules\Webhooks\WebhookHelper;

final class EntryWasUpdatedTest extends BaseTestCase
{
    use Database;
    use EventDispatcher;
    use Laravel;
    use WebhookHelper;

    public function testItReturnsGrantColumnatorIfHasGrantStatus(): void
    {
        $entry = $this->muffin(Entry::class);
        $event = new EntryWasUpdated($entry);
        $this->assertTrue($event->columnatorArea() === ManageEntriesColumnator::key());

        $entry->grantStatusId = 1;
        $this->assertTrue($event->columnatorArea() === ManageGrantsColumnator::key());
    }

    public function testItCanCreateWebhookPayload()
    {
        Bus::fake(DebounceableCallWebhookCommand::class);
        $this->createWebhook('get', ['EntryWasUpdated']);
        $entry = $this->muffin(Entry::class, ['grant_status_id' => ($this->muffin(GrantStatus::class))->id]);

        app(EventListener::class)->handle(new EntryWasUpdated($entry));

        Bus::assertDispatched(DebounceableCallWebhookCommand::class, 1);
    }
}
