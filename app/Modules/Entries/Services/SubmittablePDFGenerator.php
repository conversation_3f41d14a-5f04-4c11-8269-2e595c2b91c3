<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\PDF\Exceptions\PDFGenerationException;
use AwardForce\Library\PDF\PDFGenerator;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\AttachmentTypes;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Factory as VisibleFieldsFactory;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\VisibleFields;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Judging\Services\FieldCriteriaBlocks;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoringCriteria\Repositories\ScoringCriterionRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Platform\Events\EventDispatcher;
use Tectonic\LaravelLocalisation\Translator\Engine;

class SubmittablePDFGenerator extends PDFGenerator
{
    use EventDispatcher;

    public function __construct(
        protected AttachmentTypes $attachmentTypes,
        protected ScoreSetRepository $scoreSets,
        protected VisibleFieldsFactory $visibleFields,
        protected VisibleAttachments $visibleAttachments,
        protected Engine $translator,
        private TabRepository $tabs,
        private ScoringCriterionRepository $scoringCriteria
    ) {
        parent::__construct();
    }

    public function generatePDF(Submittable $submittable, bool $forManager = false, ?ScoreSetCollection $scoreSets = null, bool $htmlOnly = false)
    {
        try {
            if ($scoreSets) {
                return $this->generateJudgesPDF($submittable, $scoreSets, $htmlOnly);
            }

            return $forManager
                ? $this->generateManagerPDF(submittable: $submittable, htmlOnly: $htmlOnly)
                : $this->generateEntrantPDF(submittable: $submittable, htmlOnly: $htmlOnly);
        } catch (PDFGenerationException $ex) {
            $this->handleException($ex, $submittable);
        }
    }

    /**
     * Generate blank PDF
     *
     *
     * @throws PDFGenerationException
     * @throws \Throwable
     */
    public function generateBlankFormPDF(Category $category, ?Chapter $chapter = null, bool $htmlOnly = false): mixed
    {
        $roles = Consumer::isUser() ? Consumer::get()->user()->roles->pluck('id') : 'all';
        if ($contentBlock = app(ContentBlockRepository::class)->getByKeysForRoles('blank-entry-pdf-info', $roles)->first()) {
            $contentBlock = EntryFormService::mapContentBlock($contentBlock);
        }
        $category = $this->translator->shallow($category);
        $sponsors = $this->getCategorySponsors($category, $chapter);
        $tabs = $this->getCategoryTabs($category);

        $visibleFields = Consumer::isGuestOrEntrant() ? $this->visibleFields->entrant() : $this->visibleFields->manager();

        $fields = $this->translator->shallow($visibleFields->getForCategory($category));

        $tabbedFields = $tabs->map(fn(Tab $tab) => collect([
            'tab' => $tab,
            'fields' => $fields->where('tab_id', $tab->id)
                ->sortBy(fn($field) => $field->created_at->timestamp)
                ->sortBy(fn($field) => $field->order)
                ->sortBy(fn($field) => $field->resource === Field::RESOURCE_CONTRIBUTORS)->values(),
        ]));

        $maxOptions = 10;
        $blankPdf = true;
        $view = view('entry.pdf.blank-form', compact('category', 'htmlOnly', 'contentBlock', 'sponsors', 'tabs', 'tabbedFields', 'maxOptions', 'blankPdf'));

        return $htmlOnly ? $view : $this->create($view);
    }

    /**
     * Generate a submittable PDF for entrants
     *
     * @return mixed
     *
     * @throws PDFGenerationException
     * @throws \Throwable
     */
    public function generateEntrantPDF(Submittable $submittable, bool $htmlOnly = false)
    {
        return $this->generateSubmittablePDF($this->visibleFields->entrant(), $submittable, $htmlOnly, true);
    }

    public function generateManagerPDF(Submittable $submittable, bool $htmlOnly = false)
    {
        return $this->generateSubmittablePDF($this->visibleFields->manager(), $submittable, $htmlOnly, false);
    }

    private function generateSubmittablePDF(VisibleFields $visibleFields, Submittable $submittable, bool $htmlOnly, bool $ignoreHiddenAttachments)
    {
        $fields = $this->translator->translate($visibleFields->forSubmittable($submittable)->applyDefaultFiltering());
        $contributors = $this->translator->translate($visibleFields->forContributors($submittable));
        $userFields = $this->translator->translate($visibleFields->forUser($submittable));
        $referees = $this->translator->translate($visibleFields->forReferees($submittable));

        $attachments = $this->visibleAttachments->attachments($submittable, [], $ignoreHiddenAttachments);
        $attachmentFields = $visibleFields->forAttachments($submittable, $attachments, []);
        $attachments = $this->translator->translate($attachmentFields);

        $links = $this->visibleAttachments->filteredLinks($submittable);

        $tabbedFields = $this->tabbedFields($this->getTabs($submittable), $fields);

        $view = view('entry.pdf.entrant', [
            'submittable' => $submittable,
            'tabbedFields' => $tabbedFields,
            'attachments' => $attachments,
            'links' => $links,
            'contributors' => $contributors,
            'referees' => $referees,
            'userFields' => $userFields,
            'htmlOnly' => $htmlOnly,
        ]);

        return $htmlOnly ? $view : $this->create($view);
    }

    /**
     * Generate a submittable PDF for judges
     *
     * @param  bool  $htmlOnly
     * @return mixed
     *
     * @throws PDFGenerationException
     * @throws \Throwable
     */
    public function generateJudgesPDF(Submittable $submittable, ?ScoreSetCollection $scoreSets = null, $htmlOnly = false)
    {
        [$visibleFields, $scoreSets, $mode] = $this->visibleFieldsFromScoreSets($submittable, $scoreSets);
        $fields = $this->translator->translate($visibleFields->forSubmittableAllowEmpty($submittable, true));
        $contributors = $this->translator->translate($visibleFields->forContributors($submittable));

        $attachments = $this->visibleAttachments->attachments($submittable, $scoreSets);
        $attachmentTypes = $this->attachmentTypes($submittable, $scoreSets);
        $attachmentFields = $visibleFields->forAttachments($submittable, $attachments, $attachmentTypes);
        $attachments = $this->translator->translate($attachmentFields);

        $links = $this->visibleAttachments->filteredLinks($submittable);

        $criteria = $this->getCriteria($submittable, $scoreSets->pluck('id')->all());

        $additionalCriteria = $criteria->where('fieldId', false);

        $tabbedFields = $this->criteriaTabbedFields($this->getTabs($submittable), $fields, $criteria);

        $scoringBoxes = $scoreSets->settingEnabled('displayScoringBoxes');

        $displayEntryName = $scoreSets->settingAllEnabled('displayEntryName');
        $displayEntrantName = $scoreSets->settingAllEnabled('displayEntrantName');

        $view = view('entry.pdf.judge', compact(
            'submittable',
            'tabbedFields',
            'contributors',
            'mode',
            'scoreSets',
            'attachments',
            'links',
            'scoringBoxes',
            'additionalCriteria',
            'displayEntryName',
            'displayEntrantName'
        ));

        return $htmlOnly ? $view : $this->create($view);
    }

    /**
     * @return Collection
     */
    protected function getTabs(Submittable $submittable)
    {
        if (! $submittable->getCategoryId()) {
            return collect();
        }

        $tabs = $this->tabs->forCategory($submittable->getCategory(), Tab::RESOURCE_ENTRIES, $submittable->getSeasonId());

        return $this->translator->translate($tabs);
    }

    /**
     * @return Collection
     */
    protected function tabbedFields(Collection $tabs, Collection $fields)
    {
        return $fields->groupBy('tab_id')
            ->map(function ($fields, $tabId) use ($tabs) {
                return collect([
                    'tab' => $tabs->firstWhere('id', $tabId),
                    'fields' => $fields,
                ]);
            })->sortBy('tab.order');
    }

    /**
     * @return Collection
     */
    protected function criteriaTabbedFields(Collection $tabs, Collection $fields, Collection $criteria)
    {
        return $fields->groupBy('tab_id')
            ->map(function ($fields, $tabId) use ($tabs, $criteria) {
                return collect([
                    'tab' => $tabs->firstWhere('id', $tabId),
                    'blocks' => (new FieldCriteriaBlocks)->generate($fields, $criteria),
                ]);
            })->filter(function ($fields) {
                return $fields->get('blocks')->isNotEmpty();
            })->sortBy(function ($fields) {
                return ! $fields->get('tab');
            });
    }

    /**
     * Returns an array with attachment types for the given submittable and score set. If there is no score set available
     * use all VIP judging score sets from the submittable's season
     *
     * @param  ScoreSet  $scoreSet
     * @return array
     */
    protected function attachmentTypes(Submittable $submittable, ?ScoreSetCollection $scoreSets = null)
    {
        return $this->attachmentTypes->fromScoreSets($scoreSets, $submittable->attachmentTypes());
    }

    /**
     * Something went wrong, handle PDFGenerationException
     */
    protected function handleException(PDFGenerationException $ex, Submittable $submittable)
    {
        Log::debug($ex);

        if ($submittable instanceof Entry) {
            $submittable->flagIssue(trans('entries.health_status.issues.pdf'));
            $this->dispatch($submittable->releaseEvents());
        }
    }

    protected function visibleFieldsFromScoreSets(Submittable $submittable, ?ScoreSetCollection $scoreSets = null)
    {
        if (! $scoreSets || $scoreSets->isEmpty()) {
            $scoreSets = $this->scoreSets->getForSeasonAndMode($submittable->getSeasonId(), ScoreSet::MODE_VIP);

            return [$this->visibleFields->vipJudging($scoreSets), $scoreSets, ScoreSet::MODE_VIP];
        }

        if ($scoreSets->count() > 1) {
            return [$this->visibleFields->vipJudging($scoreSets), $scoreSets, ScoreSet::MODE_VIP];
        }

        return [$this->visibleFields->scoreSetBased($scoreSets->first()), $scoreSets, $scoreSets->first()->mode];
    }

    public function getCriteria(Submittable $submittable, array $scoreSets)
    {
        $criteria = $this->scoringCriteria->getForScoring($submittable->getFormId(), $submittable->getCategoryId(), $scoreSets);

        return $this->translator->translate($criteria);
    }

    /**
     * @param  mixed  $category
     */
    private function getCategorySponsors(Category $category, ?Chapter $chapter): array
    {
        $sponsors = collect();
        if (feature_enabled('sponsors')) {
            $this->getResourceImages($category, File::RESOURCE_CATEGORIES)
                ->each(fn(array $file) => $sponsors->push($file));

            $chapterImages = $chapter ? $this->getResourceImages($chapter, File::RESOURCE_CHAPTERS)
                : $category->chapters
                    ->map(fn(Chapter $chapter) => $this->getResourceImages($chapter, File::RESOURCE_CHAPTERS))
                    ->flatten(1);

            $chapterImages->each(fn(array $file) => $sponsors->push($file));
        }

        return $sponsors->toArray();
    }

    /**
     * @return mixed
     */
    private function getCategoryTabs(Category $category): \Illuminate\Database\Eloquent\Collection
    {
        /** @var \Illuminate\Database\Eloquent\Collection $tabs */
        $tabs = $this->translator->shallow($this->tabs->forCategory($category)
            ->loadMissing('contentBlock'))
            ->filter(fn(Tab $tab) => Consumer::isGuestOrEntrant() ? $tab->visibleToEntrants : true)
            ->map(function (Tab $tab) {
                if ($tab->contentBlock) {
                    $tab->setRelation('contentBlock', EntryFormService::mapContentBlock($tab->contentBlock));
                }

                return $tab;
            })
            ->sortBy('order')->values();

        return $this->filterCategoryTabs($tabs);
    }

    private function filterCategoryTabs(\Illuminate\Database\Eloquent\Collection $tabs): \Illuminate\Database\Eloquent\Collection
    {
        $tabEligibilityIndex = $tabs->search(fn(Tab $tab) => $tab->type === Tab::TYPE_ELIGIBILITY && (bool) $tab->getSetting('ineligible-hide-tabs'));

        if ($tabEligibilityIndex) {
            /** @var Tab $tab */
            foreach ($tabs as $index => $tab) {
                if ($index > $tabEligibilityIndex) {
                    $tabs->forget($index);
                }
            }
        }

        /** @var Tab $tab */
        foreach ($tabs as $index => $tab) {
            if (Consumer::isEntrant() && ! $tab->visible_to_entrants) {
                $tabs->forget($index);
            }
        }

        return $tabs->values();
    }

    private function getResourceImages(Category|Chapter $model, string $resource): Collection
    {
        return app(FileRepository::class)
            ->getByResourceId($resource, $model->id)
            ->map(function (File $file) {
                return [
                    'original' => $file->original,
                    'image' => imgix($file->file, params: config('ui.images.gallery.small')),
                ];
            });
    }
}
