<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddConditionalFieldsToFieldsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->boolean('conditional')->default(false)->after('max_file_size');
            $table->string('conditional_visibility')->nullable()->after('conditional');
            $table->bigInteger('conditional_field_id')->unsigned()->nullable()->after('conditional_visibility');
            $table->string('conditional_pattern')->nullable()->after('conditional_field_id');
            $table->string('conditional_value')->nullable()->after('conditional_pattern');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fields', function (Blueprint $table) {
            $table->dropColumn('conditional');
            $table->dropColumn('conditional_visibility');
            $table->dropColumn('conditional_field_id');
            $table->dropColumn('conditional_pattern');
            $table->dropColumn('conditional_value');
        });
    }
}
