<?php

use AwardForce\Modules\Features\Data\Feature;
use Illuminate\Database\Migrations\Migration;
use Platform\Features\Feature as PlatformFeature;

class AddMultiformFeature extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Feature::where('feature', 'multiform')->exists()) {
            return;
        }

        // Add system-level
        Feature::add(null, new PlatformFeature('multiform', Feature::ENABLED), Feature::ENABLED);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Feature::where('feature', 'multiform')->delete();
    }
}
