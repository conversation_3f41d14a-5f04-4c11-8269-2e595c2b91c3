<?php

namespace AwardForce\Http\Requests\Judging;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Judging\Services\TagEntryPermissions;

class TagEntryRequest extends FormRequest
{
    public function __construct(
        private CurrentAssignments $assignments,
        private TagEntryPermissions $tagEntryPermissions
    ) {
    }

    public function entry(): Entry
    {
        return $this->route('entry');
    }

    public function authorize()
    {
        return $this->tagEntryPermissions->canTagEntry($this->entry());
    }

    public function rules()
    {
        return [];
    }
}
