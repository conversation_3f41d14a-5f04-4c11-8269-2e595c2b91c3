<?php

namespace AwardForce\Http\Requests\User;

use AwardForce\Http\Requests\SelectedModelsRequest;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Validation\UserIsNotAccountOwner;

class DeleteUserRequest extends SelectedModelsRequest
{
    /**
     * Validate the selected models exist in the database.
     *
     * @return array
     */
    public function rules(array $rules = [])
    {
        $rules = array_merge(parent::rules($rules), $rules);

        $rules['selected'] = array_merge(
            explode('|', $rules['selected'] ?? ''),
            [new UserIsNotAccountOwner]
        );

        return $rules;
    }

    protected function getIds(): array
    {
        return app(UserRepository::class)->getIdsForAccount(current_account_id(), $this->get('selected', []));
    }
}
